"use client";
import * as React from "react";
import {
  App<PERSON><PERSON>,
  Box,
  Container,
  Drawer,
  IconButton,
  List,
  ListItem,
  Toolbar,
  Typography,
  Button,
  Divider,
  useTheme,
} from "@mui/material";

const drawerWidth = 240;
const navItems = ["Home", "About", "Contact"];

export default function Header() {
  const theme = useTheme();
  const [mobileOpen, setMobileOpen] = React.useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen((prevState) => !prevState);
  };

  const drawer = (
    <Box onClick={handleDrawerToggle} sx={{ textAlign: "center" }}>
      <Typography variant="h6" sx={{ my: 2 }}>
        MUI
      </Typography>
      <Divider />
      <List>
        {navItems.map((item) => (
          <ListItem key={item} disablePadding sx={{ px: 2, py: 1 }}>
            <Button
              fullWidth
              variant="contained"
              sx={{
                backgroundColor: "#000000",
                color: "#FFFFFF",
                borderRadius: "25px",
                fontWeight: 500,
                letterSpacing: "0.5px",
                textTransform: "capitalize",
                py: 1.5,
                transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                "&:hover": {
                  backgroundColor: "#1A1A1A",
                  transform: "translateY(-1px)",
                  boxShadow: "0 6px 20px rgba(0, 0, 0, 0.25)",
                },
                "&:active": {
                  backgroundColor: "#000000",
                  transform: "translateY(0)",
                  transition: "all 0.1s ease",
                },
              }}
            >
              {item}
            </Button>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: "flex" }}>
      <AppBar
        component="nav"
        position="fixed"
        elevation={0}
        sx={{
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1100,
          borderRadius: "0 0 32px 32px",
          overflow: "hidden",
          background: "transparent",
          boxShadow: theme.shadows[1],
        }}
      >
        {/* Glass Layers */}
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            backdropFilter: "blur(8px)",
            filter: "saturate(120%) brightness(1.15)",
            zIndex: 0,
          }}
        />
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            backgroundColor: "rgba(255, 255, 255, 0.04)",
            zIndex: 1,
          }}
        />
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            zIndex: 2,
            boxShadow: `
              inset 1px 1px 0 rgba(255, 255, 255, 0.1),
              inset 0 0 5px rgba(255, 255, 255, 0.08)
            `,
            borderRadius: "inherit",
          }}
        />

        {/* Content */}
        <Container
          sx={{
            position: "relative",
            zIndex: 3,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            minHeight: "64px",
          }}
        >
          <Toolbar disableGutters sx={{ width: "100%" }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { sm: "none" } }}
            >
              icon
            </IconButton>
            <Typography
              variant="h6"
              component="div"
              sx={{ flexGrow: 1, display: { xs: "none", sm: "block" } }}
            >
              MUI
            </Typography>
            <Box sx={{ display: { xs: "none", sm: "block" } }}>
              {navItems.map((item) => (
                <Button key={item} variant="text">
                  {item}
                </Button>
              ))}
            </Box>
          </Toolbar>
        </Container>
      </AppBar>

      {/* Drawer */}
      <nav>
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: "block", sm: "none" },
            "& .MuiDrawer-paper": {
              boxSizing: "border-box",
              width: drawerWidth,
            },
          }}
        >
          {drawer}
        </Drawer>
      </nav>

      {/* Add top margin to offset fixed AppBar */}
      <Box sx={{ height: "80px" }} />
    </Box>
  );
}
